{"timestamp": "2025-08-04T03:48:11.592291", "test_results": {"accuracy": 0.7431693989071039, "qwk": 0.7042672682762925, "auc": 0.8627352813872102, "precision": 0.6119439384739931, "recall": 0.7431693989071039, "f1": 0.6648055596530134}, "training_history": {"train_loss": [1.3425843385250673, 1.1733148810656175, 1.090343502552613, 1.0261392599862555, 0.9915927998397661, 0.9575311768314113, 0.9337064040743787, 0.9170079023941703, 0.9041583486225294, 0.8990569075812465, 0.8912786005631738, 0.8788904651351597, 0.8721241348463556, 0.8769902878481409, 0.8693848304126574], "val_accuracy": [0.5519125683060109, 0.6530054644808743, 0.6885245901639344, 0.6857923497267759, 0.6939890710382514, 0.7021857923497268, 0.6994535519125683, 0.7021857923497268, 0.7021857923497268, 0.7021857923497268, 0.6994535519125683, 0.7021857923497268, 0.6994535519125683, 0.7021857923497268, 0.6994535519125683], "val_qwk": [0.22181918785158772, 0.5461981900743937, 0.6428314415622649, 0.6376554756982048, 0.6485133844786144, 0.6719672726073043, 0.668379954531976, 0.671533811238479, 0.6717506850219537, 0.6717506850219537, 0.679594331208087, 0.6717506850219537, 0.667942331166903, 0.6717506850219537, 0.667942331166903], "val_auc": [0.6281422627556744, 0.720957022312517, 0.7732070363442178, 0.8086753819658069, 0.8233087897413348, 0.8327352983917115, 0.839462234471795, 0.8480560671969076, 0.8440239612306153, 0.8502660494139096, 0.8507334092438011, 0.8565041605509567, 0.8533293266099629, 0.8540920071049849, 0.8561074951027574], "val_precision": [0.4169352202139088, 0.48860609817697576, 0.5269407239563594, 0.5292920855205496, 0.5395093475560635, 0.5479082481284586, 0.5440432468353688, 0.5501526808809271, 0.5490150767002436, 0.5490150767002436, 0.5484511658781457, 0.5490150767002436, 0.5461861087014012, 0.5490150767002436, 0.5461861087014012], "val_recall": [0.5519125683060109, 0.6530054644808743, 0.6885245901639344, 0.6857923497267759, 0.6939890710382514, 0.7021857923497268, 0.6994535519125683, 0.7021857923497268, 0.7021857923497268, 0.7021857923497268, 0.6994535519125683, 0.7021857923497268, 0.6994535519125683, 0.7021857923497268, 0.6994535519125683], "val_f1": [0.44191695292451766, 0.5585756501996902, 0.5961920283699992, 0.5960341258454432, 0.604778863797093, 0.6125771450757396, 0.609511553181474, 0.6135591151324884, 0.6130625248904819, 0.6130625248904819, 0.6114620449814591, 0.6130625248904819, 0.6104644833250743, 0.6130625248904819, 0.6104644833250743], "learning_rate": [0.0001, 9.890738003669029e-05, 9.567727288213005e-05, 9.045084971874738e-05, 8.345653031794292e-05, 7.500000000000001e-05, 6.545084971874738e-05, 5.522642316338269e-05, 4.4773576836617344e-05, 3.454915028125264e-05, 2.5000000000000018e-05, 1.654346968205711e-05, 9.549150281252635e-06, 4.322727117869952e-06, 1.092619963309716e-06], "epochs": [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15], "best_epoch": 11, "best_qwk": 0.679594331208087}, "training_config": {"model": "ResNet50", "num_classes": 5, "epochs": 15, "batch_size": 32, "device": "cuda", "optimizer": "<PERSON>", "scheduler": "CosineAnnealingLR", "criterion": "CrossEntropyLoss"}}