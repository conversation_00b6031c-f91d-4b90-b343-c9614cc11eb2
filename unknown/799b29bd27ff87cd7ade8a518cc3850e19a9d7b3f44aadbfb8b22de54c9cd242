#!/usr/bin/env python3
"""
ODIR数据集加载器
支持ImageFolder标准格式的ODIR糖尿病视网膜病变数据集
"""

import os
import torch
from torch.utils.data import DataLoader
import torchvision.transforms as transforms
from torchvision.datasets import ImageFolder

# 设备配置
device = torch.device('cuda' if torch.cuda.is_available() else 'mps' if torch.backends.mps.is_available() else 'cpu')

# 数据增强和预处理
train_transform = transforms.Compose([
    transforms.Resize((224, 224)),  # 调整图片大小
    transforms.RandomRotation(10),  # 随机旋转
    transforms.RandomHorizontalFlip(p=0.5),  # 随机水平翻转
    transforms.RandomVerticalFlip(p=0.2),    # 随机垂直翻转（眼底图像适用）
    transforms.ColorJitter(brightness=0.1, contrast=0.1, saturation=0.1, hue=0.05),  # 颜色抖动
    transforms.ToTensor(),
    transforms.Normalize(mean=[0.485, 0.456, 0.406], std=[0.229, 0.224, 0.225]),  # ImageNet标准化
])

val_test_transform = transforms.Compose([
    transforms.Resize((224, 224)),
    transforms.ToTensor(),
    transforms.Normalize(mean=[0.485, 0.456, 0.406], std=[0.229, 0.224, 0.225]),
])


class ODIRDataLoader:
    """ODIR数据集加载器类"""
    
    def __init__(self, data_dir='./data/ODIR_ImageFolder', batch_size=32, num_workers=4):
        """
        初始化ODIR数据加载器
        
        Args:
            data_dir (str): 数据集根目录路径
            batch_size (int): 批次大小
            num_workers (int): 数据加载进程数
        """
        self.data_dir = data_dir
        self.batch_size = batch_size
        self.num_workers = num_workers

        # 构建各个数据集路径
        self.train_dir = os.path.join(data_dir, 'train')
        self.val_dir = os.path.join(data_dir, 'val')
        self.test_dir = os.path.join(data_dir, 'test')

        # 验证目录是否存在
        self._validate_directories()

        # 创建数据集
        self.train_dataset = ImageFolder(self.train_dir, transform=train_transform)
        self.val_dataset = ImageFolder(self.val_dir, transform=val_test_transform)
        self.test_dataset = ImageFolder(self.test_dir, transform=val_test_transform)

        # 获取类别信息
        self.classes = self.train_dataset.classes  # ['0', '1']
        self.class_names = ['Normal', 'Diabetic_Retinopathy']  # 更友好的类别名称
        self.num_classes = len(self.classes)
        
        # 打印数据集信息
        self._print_dataset_info()

    def _validate_directories(self):
        """验证数据目录是否存在"""
        for split, path in [('train', self.train_dir), ('val', self.val_dir), ('test', self.test_dir)]:
            if not os.path.exists(path):
                raise FileNotFoundError(f"{split} 目录不存在: {path}")
            
            # 检查类别目录
            for class_dir in ['0', '1']:
                class_path = os.path.join(path, class_dir)
                if not os.path.exists(class_path):
                    raise FileNotFoundError(f"类别目录不存在: {class_path}")

    def _print_dataset_info(self):
        """打印数据集信息"""
        print("=" * 50)
        print("ODIR数据集信息")
        print("=" * 50)
        print(f"数据目录: {self.data_dir}")
        print(f"类别数量: {self.num_classes}")
        print(f"类别映射: {dict(zip(self.classes, self.class_names))}")
        print(f"训练集: {len(self.train_dataset)} 张图片")
        print(f"验证集: {len(self.val_dataset)} 张图片")
        print(f"测试集: {len(self.test_dataset)} 张图片")
        print(f"批次大小: {self.batch_size}")
        print(f"工作进程: {self.num_workers}")
        print("=" * 50)

    def get_dataloaders(self):
        """
        获取训练、验证、测试的DataLoader
        
        Returns:
            tuple: (train_loader, val_loader, test_loader)
        """
        train_loader = DataLoader(
            self.train_dataset,
            batch_size=self.batch_size,
            shuffle=True,
            num_workers=self.num_workers,
            pin_memory=True if torch.cuda.is_available() else False,
            drop_last=True  # 丢弃最后一个不完整的批次
        )

        val_loader = DataLoader(
            self.val_dataset,
            batch_size=self.batch_size,
            shuffle=False,
            num_workers=self.num_workers,
            pin_memory=True if torch.cuda.is_available() else False
        )

        test_loader = DataLoader(
            self.test_dataset,
            batch_size=self.batch_size,
            shuffle=False,
            num_workers=self.num_workers,
            pin_memory=True if torch.cuda.is_available() else False
        )

        return train_loader, val_loader, test_loader

    def get_class_weights(self):
        """
        计算类别权重，用于处理类别不平衡（虽然ODIR数据集是平衡的）
        
        Returns:
            torch.Tensor: 类别权重张量
        """
        # 统计各类别样本数量
        class_counts = [0] * self.num_classes
        for _, label in self.train_dataset:
            class_counts[label] += 1
        
        # 计算权重（总样本数 / (类别数 * 各类别样本数)）
        total_samples = sum(class_counts)
        weights = [total_samples / (self.num_classes * count) for count in class_counts]
        
        return torch.FloatTensor(weights)

    def get_sample_for_visualization(self, split='train', num_samples=8):
        """
        获取用于可视化的样本
        
        Args:
            split (str): 数据集分割 ('train', 'val', 'test')
            num_samples (int): 样本数量
            
        Returns:
            tuple: (images, labels, class_names)
        """
        if split == 'train':
            dataset = self.train_dataset
        elif split == 'val':
            dataset = self.val_dataset
        elif split == 'test':
            dataset = self.test_dataset
        else:
            raise ValueError("split must be 'train', 'val', or 'test'")
        
        # 随机选择样本
        indices = torch.randperm(len(dataset))[:num_samples]
        images, labels = [], []
        
        for idx in indices:
            img, label = dataset[idx]
            images.append(img)
            labels.append(label)
        
        return torch.stack(images), torch.tensor(labels), self.class_names


def get_odir_dataloaders(data_dir='./data/ODIR_ImageFolder', batch_size=32, num_workers=4):
    """
    便捷函数：直接获取ODIR数据加载器
    
    Args:
        data_dir (str): 数据集根目录路径
        batch_size (int): 批次大小
        num_workers (int): 数据加载进程数
        
    Returns:
        tuple: (train_loader, val_loader, test_loader)
    """
    dataloader = ODIRDataLoader(data_dir, batch_size, num_workers)
    return dataloader.get_dataloaders()


if __name__ == "__main__":
    """测试数据加载器"""
    print("测试ODIR数据加载器...")
    
    try:
        # 创建数据加载器
        odir_loader = ODIRDataLoader(batch_size=16, num_workers=2)
        
        # 获取DataLoader
        train_loader, val_loader, test_loader = odir_loader.get_dataloaders()
        
        print(f"\nDataLoader创建成功!")
        print(f"训练批次数: {len(train_loader)}")
        print(f"验证批次数: {len(val_loader)}")
        print(f"测试批次数: {len(test_loader)}")
        
        # 测试数据加载
        print(f"\n测试数据加载...")
        train_iter = iter(train_loader)
        images, labels = next(train_iter)
        
        print(f"批次图片形状: {images.shape}")
        print(f"批次标签形状: {labels.shape}")
        print(f"标签分布: {torch.bincount(labels)}")
        
        # 获取类别权重
        class_weights = odir_loader.get_class_weights()
        print(f"类别权重: {class_weights}")
        
        print(f"\n✅ ODIR数据加载器测试通过!")
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
