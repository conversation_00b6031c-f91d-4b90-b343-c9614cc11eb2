import torch
import torch.nn as nn
import torchvision.models as models
from torchvision.models import ResNet50_Weights
from torch.nn import functional as F
from .EMA import EMA


class DropPath(nn.Module):
    def __init__(self, drop_prob: float = 0.0):
        super(DropPath, self).__init__()
        self.drop_prob = drop_prob

    def forward(self, x: torch.Tensor) -> torch.Tensor:
        if self.drop_prob == 0.0 or not self.training:
            return x

        # 在训练过程中按给定的概率丢弃整个路径
        keep_prob = 1.0 - self.drop_prob
        mask = torch.ones(x.size(0), 1, 1, 1).to(x.device) * keep_prob
        mask = mask + torch.randn_like(mask) * 0.0  # 确保均值为1，方差不变
        mask = F.relu(mask)

        return x * mask / keep_prob  # 进行缩放补偿


class ResNet50_EMA_AllStages(nn.Module):
    def __init__(self, num_classes=1000, drop_prob=0.5, ema_factor=8, use_residual=True):
        super(ResNet50_EMA_AllStages, self).__init__()

        # 加载预训练的 ResNet50
        self.model = models.resnet50(weights=ResNet50_Weights.IMAGENET1K_V2)
        
        self.drop_path = DropPath(drop_prob)
        self.use_residual = use_residual
        
        # 获取各层的通道数
        # layer1输出: 256通道  (56x56)
        # layer2输出: 512通道  (28x28)
        # layer3输出: 1024通道 (14x14)
        # layer4输出: 2048通道 (7x7)
        
        # 在所有stage后添加EMA注意力模块
        self.ema_layer1 = EMA(256, factor=ema_factor)   # layer1后的EMA
        self.ema_layer2 = EMA(512, factor=ema_factor)   # layer2后的EMA
        self.ema_layer3 = EMA(1024, factor=ema_factor)  # layer3后的EMA
        self.ema_layer4 = EMA(2048, factor=ema_factor)  # layer4后的EMA
        
        # 修改最后的分类层
        self.model.fc = nn.Linear(self.model.fc.in_features, num_classes)

    def forward(self, x):
        # 通过ResNet50的前几层
        x = self.model.conv1(x)
        x = self.model.bn1(x)
        x = self.model.relu(x)
        x = self.model.maxpool(x)

        # layer1 + EMA注意力
        x = self.model.layer1(x)
        if self.use_residual:
            x = x + self.ema_layer1(x)  # 残差连接
        else:
            x = self.ema_layer1(x)  # 直接替换
        x = self.drop_path(x)  # 应用drop_path

        # layer2 + EMA注意力
        x = self.model.layer2(x)
        if self.use_residual:
            x = x + self.ema_layer2(x)  # 残差连接
        else:
            x = self.ema_layer2(x)  # 直接替换
        x = self.drop_path(x)  # 应用drop_path

        # layer3 + EMA注意力
        x = self.model.layer3(x)
        if self.use_residual:
            x = x + self.ema_layer3(x)  # 残差连接
        else:
            x = self.ema_layer3(x)  # 直接替换
        x = self.drop_path(x)  # 应用drop_path

        # layer4 + EMA注意力
        x = self.model.layer4(x)
        if self.use_residual:
            x = x + self.ema_layer4(x)  # 残差连接
        else:
            x = self.ema_layer4(x)  # 直接替换
        x = self.drop_path(x)  # 应用drop_path

        # 全局平均池化和分类
        x = self.model.avgpool(x)
        x = torch.flatten(x, 1)
        x = self.model.fc(x)

        return x


if __name__ == "__main__":
    # 创建一个 ResNet50_EMA_AllStages 模型
    model = ResNet50_EMA_AllStages(num_classes=5, drop_prob=0.5, ema_factor=8)
    
    # 打印模型参数数量
    total_params = sum(p.numel() for p in model.parameters())
    trainable_params = sum(p.numel() for p in model.parameters() if p.requires_grad)
    print(f"Total parameters: {total_params:,}")
    print(f"Trainable parameters: {trainable_params:,}")

    # 测试模型的输入输出
    x = torch.randn(2, 3, 224, 224)  # 输入是一个 224x224 的 RGB 图像
    output = model(x)
    print(f"Input shape: {x.shape}")
    print(f"Output shape: {output.shape}")
    
    # 测试各个EMA模块的形状
    print("\n=== EMA模块测试 ===")
    test_layer1 = torch.randn(2, 256, 56, 56)
    test_layer2 = torch.randn(2, 512, 28, 28)
    test_layer3 = torch.randn(2, 1024, 14, 14)
    test_layer4 = torch.randn(2, 2048, 7, 7)
    
    ema1_out = model.ema_layer1(test_layer1)
    ema2_out = model.ema_layer2(test_layer2)
    ema3_out = model.ema_layer3(test_layer3)
    ema4_out = model.ema_layer4(test_layer4)
    
    print(f"Layer1 EMA: {test_layer1.shape} -> {ema1_out.shape}")
    print(f"Layer2 EMA: {test_layer2.shape} -> {ema2_out.shape}")
    print(f"Layer3 EMA: {test_layer3.shape} -> {ema3_out.shape}")
    print(f"Layer4 EMA: {test_layer4.shape} -> {ema4_out.shape}")
    
    # 计算EMA模块的参数数量
    ema_params = (sum(p.numel() for p in model.ema_layer1.parameters()) +
                  sum(p.numel() for p in model.ema_layer2.parameters()) +
                  sum(p.numel() for p in model.ema_layer3.parameters()) +
                  sum(p.numel() for p in model.ema_layer4.parameters()))
    
    print(f"\nEMA模块总参数数: {ema_params:,}")
    print(f"EMA参数占比: {(ema_params / total_params * 100):.2f}%")
