{"timestamp": "2025-08-05T02:24:45.887191", "test_results": {"accuracy": 0.6501831501831502, "auc": 0.7313931489755667, "precision": 0.6501851653069987, "recall": 0.6501831501831502, "f1": 0.6501819767539373}, "training_history": {"train_loss": [0.6876792175860345, 0.6763286266145827, 0.6614197771760482, 0.6556587332411657, 0.6472192180307605, 0.6446587149100967, 0.6409311566171767, 0.6340755684466302, 0.6330863508997084, 0.6279145411298245, 0.6256825810746302, 0.6308927430382257, 0.6262867465803895, 0.6256549471541296, 0.6263269895239721], "val_accuracy": [0.6007326007326007, 0.6282051282051282, 0.6318681318681318, 0.6355311355311355, 0.6684981684981685, 0.6666666666666666, 0.673992673992674, 0.6684981684981685, 0.6703296703296703, 0.6794871794871795, 0.6776556776556777, 0.6721611721611722, 0.673992673992674, 0.673992673992674, 0.6758241758241759], "val_auc": [0.6254612298568343, 0.6742073555260369, 0.6934213527620122, 0.703873659917616, 0.7127158555729984, 0.7161775952984745, 0.7182975754404327, 0.7217324799742382, 0.720216291644863, 0.7210884353741497, 0.7237048665620095, 0.7197600933864672, 0.7201626212615224, 0.7201827476552751, 0.7205383139449073], "val_precision": [0.6039460020768431, 0.6294715447154472, 0.652779505036687, 0.6389790864061639, 0.6699231435375622, 0.6666756122106675, 0.6742264439936045, 0.6685547084720282, 0.6724116856130058, 0.6810050876813164, 0.6786143074909954, 0.6733918918918919, 0.67641657030134, 0.6745923821577154, 0.677081081081081], "val_recall": [0.6007326007326007, 0.6282051282051282, 0.6318681318681318, 0.6355311355311355, 0.6684981684981685, 0.6666666666666666, 0.673992673992674, 0.6684981684981685, 0.6703296703296703, 0.6794871794871795, 0.6776556776556777, 0.6721611721611722, 0.673992673992674, 0.673992673992674, 0.6758241758241759], "val_f1": [0.5976228144902844, 0.6272937283741388, 0.618825000434156, 0.6332564882352743, 0.6678017150098658, 0.6666621940747102, 0.6738832814345539, 0.6684703664317272, 0.6693313953488373, 0.6788138128548427, 0.677222587969743, 0.671578395561634, 0.672869010272912, 0.6737124823742698, 0.6752479106950237], "learning_rate": [0.0001, 9.890738003669029e-05, 9.567727288213005e-05, 9.045084971874738e-05, 8.345653031794292e-05, 7.500000000000001e-05, 6.545084971874738e-05, 5.522642316338269e-05, 4.4773576836617344e-05, 3.454915028125264e-05, 2.5000000000000018e-05, 1.654346968205711e-05, 9.549150281252635e-06, 4.322727117869952e-06, 1.092619963309716e-06], "epochs": [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15], "best_epoch": 11, "best_auc": 0.7237048665620095}, "training_config": {"model": "ResNet50", "num_classes": 2, "epochs": 15, "batch_size": 32, "device": "mps", "optimizer": "<PERSON>", "scheduler": "CosineAnnealingLR", "criterion": "CrossEntropyLoss"}}