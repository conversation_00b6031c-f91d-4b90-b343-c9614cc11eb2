import torch
import torch.nn as nn
import torch.nn.functional as F
import torchvision.models as models
from torchvision.models import ResNet50_Weights
from .EMA import EMA


class DropPath(nn.Module):
    def __init__(self, drop_prob: float = 0.0):
        super(DropPath, self).__init__()
        self.drop_prob = drop_prob

    def forward(self, x: torch.Tensor) -> torch.Tensor:
        if self.drop_prob == 0.0 or not self.training:
            return x

        keep_prob = 1.0 - self.drop_prob
        mask = torch.ones(x.size(0), 1, 1, 1).to(x.device) * keep_prob
        mask = mask + torch.randn_like(mask) * 0.0
        mask = F.relu(mask)

        return x * mask / keep_prob


class LightweightPPM(nn.Module):
    """
    轻量级PPM模块，用于交互式集成
    """
    def __init__(self, in_channels, down_dim, pool_scales=(1, 2, 3, 6)):
        super(LightweightPPM, self).__init__()
        
        self.pool_scales = pool_scales
        
        # 输入特征降维
        self.down_conv = nn.Sequential(
            nn.Conv2d(in_channels, down_dim, 1, bias=False),
            nn.BatchNorm2d(down_dim),
            nn.ReLU(inplace=True)
        )

        # 轻量级多尺度池化分支
        branch_dim = down_dim // len(pool_scales)
        self.pool_branches = nn.ModuleList()
        for scale in pool_scales:
            self.pool_branches.append(
                nn.Sequential(
                    nn.AdaptiveAvgPool2d(output_size=(scale, scale)),
                    nn.Conv2d(down_dim, branch_dim, kernel_size=1, bias=False),
                    nn.BatchNorm2d(branch_dim),
                    nn.ReLU(inplace=True)
                )
            )

        # 特征融合
        total_branch_dim = len(pool_scales) * branch_dim
        self.fuse = nn.Sequential(
            nn.Conv2d(total_branch_dim, down_dim, kernel_size=1, bias=False),
            nn.BatchNorm2d(down_dim),
            nn.ReLU(inplace=True)
        )

    def forward(self, x):
        # 输入特征降维
        x = self.down_conv(x)
        
        # 获取输入特征的空间尺寸
        input_size = x.size()[2:]
        
        # 多尺度池化
        pool_features = []
        for pool_branch in self.pool_branches:
            pool_feat = pool_branch(x)
            # 上采样到原始尺寸
            pool_feat = F.interpolate(pool_feat, size=input_size, 
                                    mode='bilinear', align_corners=False)
            pool_features.append(pool_feat)
        
        # 特征拼接和融合
        concat_features = torch.cat(pool_features, dim=1)
        fused_features = self.fuse(concat_features)
        
        return fused_features


class CrossScaleFusion(nn.Module):
    """
    跨尺度特征融合模块
    """
    def __init__(self, layer3_dim, layer4_dim, output_dim):
        super(CrossScaleFusion, self).__init__()
        
        # 通道对齐
        self.layer3_align = nn.Conv2d(layer3_dim, output_dim, 1, bias=False)
        self.layer4_align = nn.Conv2d(layer4_dim, output_dim, 1, bias=False)
        
        # 跨尺度注意力
        self.cross_attention = nn.Sequential(
            nn.AdaptiveAvgPool2d(1),
            nn.Conv2d(output_dim * 2, output_dim // 8, 1, bias=False),
            nn.ReLU(inplace=True),
            nn.Conv2d(output_dim // 8, 2, 1, bias=False),  # 2个权重：layer3和layer4
            nn.Softmax(dim=1)
        )
        
        # 空间注意力
        self.spatial_attention = nn.Sequential(
            nn.Conv2d(output_dim * 2, output_dim // 4, 3, padding=1, bias=False),
            nn.BatchNorm2d(output_dim // 4),
            nn.ReLU(inplace=True),
            nn.Conv2d(output_dim // 4, 1, 1, bias=False),
            nn.Sigmoid()
        )
        
        # 最终融合
        self.final_fusion = nn.Sequential(
            nn.Conv2d(output_dim * 2, output_dim, 3, padding=1, bias=False),
            nn.BatchNorm2d(output_dim),
            nn.ReLU(inplace=True),
            nn.Dropout2d(0.1)
        )

    def forward(self, layer3_feat, layer4_feat):
        # 获取layer4的空间尺寸作为目标尺寸
        target_size = layer4_feat.shape[2:]
        
        # 通道对齐
        layer3_aligned = self.layer3_align(layer3_feat)
        layer4_aligned = self.layer4_align(layer4_feat)
        
        # 将layer3特征上采样到layer4的尺寸
        layer3_upsampled = F.interpolate(layer3_aligned, size=target_size, 
                                       mode='bilinear', align_corners=False)
        
        # 特征拼接
        concat_feat = torch.cat([layer3_upsampled, layer4_aligned], dim=1)
        
        # 跨尺度通道注意力
        channel_weights = self.cross_attention(concat_feat)  # [B, 2, 1, 1]
        weighted_layer3 = layer3_upsampled * channel_weights[:, 0:1, :, :]
        weighted_layer4 = layer4_aligned * channel_weights[:, 1:2, :, :]
        
        # 空间注意力
        spatial_weight = self.spatial_attention(concat_feat)
        
        # 加权融合
        weighted_concat = torch.cat([weighted_layer3, weighted_layer4], dim=1)
        spatial_enhanced = weighted_concat * spatial_weight
        
        # 最终融合
        final_output = self.final_fusion(spatial_enhanced)
        
        return final_output


class ResNet50_EMA_PPM_Interactive(nn.Module):
    """
    ResNet50 with EMA and PPM Interactive Integration (方案D)
    
    交互式集成策略:
    1. EMA应用于Layer1-4
    2. PPM分别应用于Layer3和Layer4的EMA增强特征
    3. 跨尺度特征融合模块整合Layer3和Layer4的PPM特征
    4. 最大化特征利用，充分挖掘多层次信息
    """
    
    def __init__(self, num_classes=1000, drop_prob=0.5, ema_factor=8, 
                 ppm3_dim=256, ppm4_dim=512, fusion_dim=512):
        super(ResNet50_EMA_PPM_Interactive, self).__init__()

        # 加载预训练的 ResNet50
        self.model = models.resnet50(weights=ResNet50_Weights.IMAGENET1K_V2)
        
        self.drop_path = DropPath(drop_prob)
        
        # EMA模块 - 应用于所有stage
        self.ema_layer1 = EMA(256, factor=ema_factor)
        self.ema_layer2 = EMA(512, factor=ema_factor)
        self.ema_layer3 = EMA(1024, factor=ema_factor)
        self.ema_layer4 = EMA(2048, factor=ema_factor)
        
        # PPM模块 - 分别应用于Layer3和Layer4
        self.ppm3 = LightweightPPM(1024, ppm3_dim, pool_scales=(1, 3, 6))  # Layer3用轻量级
        self.ppm4 = LightweightPPM(2048, ppm4_dim, pool_scales=(1, 2, 3, 6))  # Layer4用完整版
        
        # 跨尺度特征融合
        self.cross_scale_fusion = CrossScaleFusion(ppm3_dim, ppm4_dim, fusion_dim)
        
        # 修改最后的分类层
        self.model.fc = nn.Linear(fusion_dim, num_classes)
        
        # 全局平均池化
        self.global_pool = nn.AdaptiveAvgPool2d(1)

    def forward(self, x):
        # 通过ResNet50的前几层
        x = self.model.conv1(x)
        x = self.model.bn1(x)
        x = self.model.relu(x)
        x = self.model.maxpool(x)

        # Layer1 + EMA
        x1 = self.model.layer1(x)
        x1_ema = x1 + self.ema_layer1(x1)
        x1_ema = self.drop_path(x1_ema)

        # Layer2 + EMA
        x2 = self.model.layer2(x1_ema)
        x2_ema = x2 + self.ema_layer2(x2)
        x2_ema = self.drop_path(x2_ema)

        # Layer3 + EMA + PPM
        x3 = self.model.layer3(x2_ema)
        x3_ema = x3 + self.ema_layer3(x3)
        x3_ema = self.drop_path(x3_ema)
        x3_ppm = self.ppm3(x3_ema)  # Layer3 PPM特征

        # Layer4 + EMA + PPM
        x4 = self.model.layer4(x3_ema)
        x4_ema = x4 + self.ema_layer4(x4)
        x4_ema = self.drop_path(x4_ema)
        x4_ppm = self.ppm4(x4_ema)  # Layer4 PPM特征

        # 跨尺度特征融合
        fused_features = self.cross_scale_fusion(x3_ppm, x4_ppm)
        
        # 全局平均池化和分类
        x = self.global_pool(fused_features)
        x = torch.flatten(x, 1)
        x = self.model.fc(x)

        return x
    
    def get_multi_scale_features(self, x):
        """
        获取多尺度特征，用于可视化和分析
        """
        # 前向传播到各层
        x = self.model.conv1(x)
        x = self.model.bn1(x)
        x = self.model.relu(x)
        x = self.model.maxpool(x)

        features = {}
        
        # Layer1
        x1 = self.model.layer1(x)
        x1_ema = x1 + self.ema_layer1(x1)
        features['layer1_original'] = x1
        features['layer1_ema'] = x1_ema

        # Layer2
        x2 = self.model.layer2(x1_ema)
        x2_ema = x2 + self.ema_layer2(x2)
        features['layer2_original'] = x2
        features['layer2_ema'] = x2_ema

        # Layer3
        x3 = self.model.layer3(x2_ema)
        x3_ema = x3 + self.ema_layer3(x3)
        x3_ppm = self.ppm3(x3_ema)
        features['layer3_original'] = x3
        features['layer3_ema'] = x3_ema
        features['layer3_ppm'] = x3_ppm

        # Layer4
        x4 = self.model.layer4(x3_ema)
        x4_ema = x4 + self.ema_layer4(x4)
        x4_ppm = self.ppm4(x4_ema)
        features['layer4_original'] = x4
        features['layer4_ema'] = x4_ema
        features['layer4_ppm'] = x4_ppm

        # 跨尺度融合
        fused_features = self.cross_scale_fusion(x3_ppm, x4_ppm)
        features['cross_scale_fused'] = fused_features
        
        return features


if __name__ == "__main__":
    # 创建交互式集成模型
    model = ResNet50_EMA_PPM_Interactive(
        num_classes=5, 
        drop_prob=0.5, 
        ema_factor=8,
        ppm3_dim=256,
        ppm4_dim=512,
        fusion_dim=512
    )
    
    # 打印模型参数数量
    total_params = sum(p.numel() for p in model.parameters())
    trainable_params = sum(p.numel() for p in model.parameters() if p.requires_grad)
    print(f"Total parameters: {total_params:,}")
    print(f"Trainable parameters: {trainable_params:,}")

    # 测试模型的输入输出
    x = torch.randn(2, 3, 224, 224)
    output = model(x)
    print(f"Input shape: {x.shape}")
    print(f"Output shape: {output.shape}")
    
    # 计算各模块的参数数量
    ema_params = (sum(p.numel() for p in model.ema_layer1.parameters()) +
                  sum(p.numel() for p in model.ema_layer2.parameters()) +
                  sum(p.numel() for p in model.ema_layer3.parameters()) +
                  sum(p.numel() for p in model.ema_layer4.parameters()))
    
    ppm3_params = sum(p.numel() for p in model.ppm3.parameters())
    ppm4_params = sum(p.numel() for p in model.ppm4.parameters())
    fusion_params = sum(p.numel() for p in model.cross_scale_fusion.parameters())
    
    print(f"\n模块参数分解:")
    print(f"  EMA模块总参数: {ema_params:,}")
    print(f"  PPM3模块参数: {ppm3_params:,}")
    print(f"  PPM4模块参数: {ppm4_params:,}")
    print(f"  跨尺度融合模块参数: {fusion_params:,}")
    print(f"  新增模块总参数: {ema_params + ppm3_params + ppm4_params + fusion_params:,}")
    print(f"  新增模块占比: {((ema_params + ppm3_params + ppm4_params + fusion_params) / total_params * 100):.2f}%")
    
    print(f"\n方案D特点:")
    print(f"1. 交互式集成 - Layer3和Layer4都有PPM处理")
    print(f"2. 跨尺度融合 - 复杂的特征融合机制")
    print(f"3. 特征利用最大化 - 充分挖掘多层次信息")
    print(f"4. 参数最多但理论性能最强")
