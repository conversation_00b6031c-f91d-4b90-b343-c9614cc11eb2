import torch
import torch.nn as nn
import torchvision.models as models
from torchvision.models import ResNet50_Weights
from torch.nn import functional as F

class DropPath(nn.Module):
    def __init__(self, drop_prob: float = 0.0):
        super(DropPath, self).__init__()
        self.drop_prob = drop_prob

    def forward(self, x: torch.Tensor) -> torch.Tensor:
        if self.drop_prob == 0.0 or not self.training:
            return x

        # 在训练过程中按给定的概率丢弃整个路径
        keep_prob = 1.0 - self.drop_prob
        mask = torch.ones(x.size(0), 1, 1, 1).to(x.device) * keep_prob
        mask = mask + torch.randn_like(mask) * 0.0  # 确保均值为1，方差不变
        mask = F.relu(mask)

        return x * mask / keep_prob  # 进行缩放补偿


class ResNet50(nn.Module):
    def __init__(self, num_classes=1000,drop_prob=0.5):
        super(ResNet50, self).__init__()

        # 加载预训练的 ResNet50
        self.model = models.resnet50(weights = ResNet50_Weights.IMAGENET1K_V2)
        
        self.drop_path = DropPath(drop_prob)
        
        self.model.fc = nn.Linear(self.model.fc.in_features, num_classes)

    def forward(self, x):
        # 通过ResNet50的各个层，在特征提取过程中应用drop_path
        x = self.model.conv1(x)
        x = self.model.bn1(x)
        x = self.model.relu(x)
        x = self.model.maxpool(x)

        # 通过残差块，在每个stage后应用drop_path
        x = self.model.layer1(x)
        x = self.drop_path(x)  # 应用drop_path

        x = self.model.layer2(x)
        x = self.drop_path(x)  # 应用drop_path

        x = self.model.layer3(x)
        x = self.drop_path(x)  # 应用drop_path

        x = self.model.layer4(x)
        x = self.drop_path(x)  # 应用drop_path

        # 全局平均池化和分类
        x = self.model.avgpool(x)
        x = torch.flatten(x, 1)
        x = self.model.fc(x)

        return x


if __name__ == "__main__":
    # 创建一个 ResNet50 模型
    model = ResNet50(num_classes=5)

    # 测试模型的输入输出
    x = torch.randn(1, 3, 224, 224)  # 输入是一个 224x224 的 RGB 图像
    output = model(x)
    print(output.shape)
