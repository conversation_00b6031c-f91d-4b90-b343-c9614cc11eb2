import torch
import torch.nn as nn
import torch.nn.functional as F
import torchvision.models as models
from torchvision.models import ResNet50_Weights


class DropPath(nn.Module):
    def __init__(self, drop_prob: float = 0.0):
        super(DropPath, self).__init__()
        self.drop_prob = drop_prob

    def forward(self, x: torch.Tensor) -> torch.Tensor:
        if self.drop_prob == 0.0 or not self.training:
            return x

        # 在训练过程中按给定的概率丢弃整个路径
        keep_prob = 1.0 - self.drop_prob
        mask = torch.ones(x.size(0), 1, 1, 1).to(x.device) * keep_prob
        mask = mask + torch.randn_like(mask) * 0.0  # 确保均值为1，方差不变
        mask = F.relu(mask)

        return x * mask / keep_prob  # 进行缩放补偿


class PPM(nn.Module):
    """
    Lightweight Pyramid Pooling Module (PPM)

    轻量级PPM模块，减少参数量同时保持多尺度上下文聚合能力
    """
    def __init__(self, in_channels, down_dim, pool_scales=(1, 2, 3, 6)):
        super(PPM, self).__init__()

        self.pool_scales = pool_scales

        # 输入特征降维 - 使用1x1卷积减少参数
        self.down_conv = nn.Sequential(
            nn.Conv2d(in_channels, down_dim, 1, bias=False),  # 1x1卷积
            nn.BatchNorm2d(down_dim),
            nn.ReLU(inplace=True)
        )

        # 轻量级多尺度池化分支 - 减少每个分支的输出通道
        branch_dim = down_dim // len(pool_scales)  # 平均分配通道数
        self.pool_branches = nn.ModuleList()
        for scale in pool_scales:
            self.pool_branches.append(
                nn.Sequential(
                    nn.AdaptiveAvgPool2d(output_size=(scale, scale)),
                    nn.Conv2d(down_dim, branch_dim, kernel_size=1, bias=False),
                    nn.BatchNorm2d(branch_dim),
                    nn.ReLU(inplace=True)
                )
            )

        # 特征融合 - 输入通道数为各分支通道数之和
        total_branch_dim = len(pool_scales) * branch_dim
        self.fuse = nn.Sequential(
            nn.Conv2d(total_branch_dim, down_dim, kernel_size=1, bias=False),
            nn.BatchNorm2d(down_dim),
            nn.ReLU(inplace=True)
        )

    def forward(self, x):
        # 输入特征降维
        x = self.down_conv(x)
        
        # 获取输入特征的空间尺寸
        input_size = x.size()[2:]
        
        # 多尺度池化
        pool_features = []
        for pool_branch in self.pool_branches:
            pool_feat = pool_branch(x)
            # 上采样到原始尺寸
            pool_feat = F.interpolate(pool_feat, size=input_size, 
                                    mode='bilinear', align_corners=False)
            pool_features.append(pool_feat)
        
        # 特征拼接和融合
        concat_features = torch.cat(pool_features, dim=1)
        fused_features = self.fuse(concat_features)
        
        return fused_features


class ResNet50_PPM(nn.Module):
    """
    ResNet50 with Pyramid Pooling Module (PPM)
    
    在ResNet50的layer4后添加PPM模块，用于捕获多尺度上下文信息
    """
    
    def __init__(self, num_classes=1000, drop_prob=0.5, ppm_dim=512, pool_scales=(1, 2, 3, 6)):
        super(ResNet50_PPM, self).__init__()

        # 加载预训练的 ResNet50
        self.model = models.resnet50(weights=ResNet50_Weights.IMAGENET1K_V2)
        
        self.drop_path = DropPath(drop_prob)
        
        # PPM模块 - 应用于layer4的输出 (2048通道)
        self.ppm = PPM(in_channels=2048, down_dim=ppm_dim, pool_scales=pool_scales)
        
        # 轻量级特征融合层 - 融合原始layer4特征和PPM特征
        self.feature_fusion = nn.Sequential(
            nn.Conv2d(2048 + ppm_dim, 2048, kernel_size=1, bias=False),  # 1x1卷积减少参数
            nn.BatchNorm2d(2048),
            nn.ReLU(inplace=True),
            nn.Dropout2d(0.1)
        )
        
        # 修改最后的分类层
        self.model.fc = nn.Linear(self.model.fc.in_features, num_classes)

    def forward(self, x):
        # 通过ResNet50的前几层
        x = self.model.conv1(x)
        x = self.model.bn1(x)
        x = self.model.relu(x)
        x = self.model.maxpool(x)

        # 通过残差块
        x = self.model.layer1(x)
        x = self.drop_path(x)

        x = self.model.layer2(x)
        x = self.drop_path(x)

        x = self.model.layer3(x)
        x = self.drop_path(x)

        x = self.model.layer4(x)
        x = self.drop_path(x)

        # 应用PPM模块
        ppm_features = self.ppm(x)
        
        # 融合原始特征和PPM特征
        fused_features = torch.cat([x, ppm_features], dim=1)
        enhanced_features = self.feature_fusion(fused_features)

        # 全局平均池化和分类
        x = self.model.avgpool(enhanced_features)
        x = torch.flatten(x, 1)
        x = self.model.fc(x)

        return x
    
    def get_features(self, x):
        """
        获取不同层的特征，用于可视化和分析
        """
        # 前向传播到layer4
        x = self.model.conv1(x)
        x = self.model.bn1(x)
        x = self.model.relu(x)
        x = self.model.maxpool(x)

        x1 = self.model.layer1(x)
        x2 = self.model.layer2(x1)
        x3 = self.model.layer3(x2)
        x4 = self.model.layer4(x3)

        # PPM特征
        ppm_features = self.ppm(x4)
        
        # 融合特征
        fused_features = torch.cat([x4, ppm_features], dim=1)
        enhanced_features = self.feature_fusion(fused_features)
        
        return {
            'layer1': x1,
            'layer2': x2,
            'layer3': x3,
            'layer4': x4,
            'ppm_features': ppm_features,
            'enhanced_features': enhanced_features
        }


if __name__ == "__main__":
    # 创建一个 ResNet50_PPM 模型
    model = ResNet50_PPM(num_classes=5, drop_prob=0.5, ppm_dim=512)
    
    # 打印模型参数数量
    total_params = sum(p.numel() for p in model.parameters())
    trainable_params = sum(p.numel() for p in model.parameters() if p.requires_grad)
    print(f"Total parameters: {total_params:,}")
    print(f"Trainable parameters: {trainable_params:,}")

    # 测试模型的输入输出
    x = torch.randn(2, 3, 224, 224)  # 输入是一个 224x224 的 RGB 图像
    output = model(x)
    print(f"Input shape: {x.shape}")
    print(f"Output shape: {output.shape}")
    
    # 测试特征提取
    print("\n=== 特征提取测试 ===")
    with torch.no_grad():
        features = model.get_features(x)
        
        for name, feat in features.items():
            print(f"{name}: {feat.shape}")
    
    # 计算PPM模块的参数数量
    ppm_params = sum(p.numel() for p in model.ppm.parameters())
    fusion_params = sum(p.numel() for p in model.feature_fusion.parameters())
    
    print(f"\nPPM模块参数数: {ppm_params:,}")
    print(f"特征融合层参数数: {fusion_params:,}")
    print(f"新增模块参数占比: {((ppm_params + fusion_params) / total_params * 100):.2f}%")
    
    print(f"\n模型特点:")
    print(f"1. PPM模块捕获多尺度上下文信息")
    print(f"2. 特征融合层整合原始特征和PPM特征")
    print(f"3. 适合需要全局上下文的图像分类任务")
