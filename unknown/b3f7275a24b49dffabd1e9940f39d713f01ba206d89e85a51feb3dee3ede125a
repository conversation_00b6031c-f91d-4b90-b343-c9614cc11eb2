import os
import torch
from torch.utils.data import DataLoader
import torchvision.transforms as transforms
from torchvision.datasets import ImageFolder


data_dir = './data/APTOS'
train_dir = os.path.join(data_dir, 'train')
val_dir = os.path.join(data_dir, 'val')
test_dir = os.path.join(data_dir, 'test')

device = torch.device('cuda' if torch.cuda.is_available() else 'mps' if torch.backends.mps.is_available() else 'cpu')

train_transform = transforms.Compose([
    transforms.RandomRotation(10),  # 随机旋转
    transforms.RandomHorizontalFlip(), # 随机水平翻转
    transforms.ColorJitter(brightness=0.1, contrast=0.1, saturation=0.1, hue=0.05),  # 添加颜色抖动
    transforms.ToTensor(),
    transforms.Normalize(mean=[0.485, 0.456, 0.406], std=[0.229, 0.224, 0.225]),
])

val_test_transform = transforms.Compose([
    transforms.ToTensor(),
    transforms.Normalize(mean=[0.485, 0.456, 0.406], std=[0.229, 0.224, 0.225]),
])


class APTOSDataLoader:
    def __init__(self, data_dir='./data/APTOS', batch_size=32, num_workers=4):
        self.data_dir = data_dir
        self.batch_size = batch_size
        self.num_workers = num_workers

        self.train_dir = os.path.join(data_dir, 'train')
        self.val_dir = os.path.join(data_dir, 'val')
        self.test_dir = os.path.join(data_dir, 'test')

        # 创建数据集
        self.train_dataset = ImageFolder(self.train_dir, transform=train_transform)
        self.val_dataset = ImageFolder(self.val_dir, transform=val_test_transform)
        self.test_dataset = ImageFolder(self.test_dir, transform=val_test_transform)

        # 获取类别信息
        self.classes = self.train_dataset.classes
        self.num_classes = len(self.classes)

    def get_dataloaders(self):
        """获取训练、验证、测试的DataLoader"""
        train_loader = DataLoader(
            self.train_dataset,
            batch_size=self.batch_size,
            shuffle=True,
            num_workers=self.num_workers,
            pin_memory=True if torch.cuda.is_available() else False
        )

        val_loader = DataLoader(
            self.val_dataset,
            batch_size=self.batch_size,
            shuffle=False,
            num_workers=self.num_workers,
            pin_memory=True if torch.cuda.is_available() else False
        )

        test_loader = DataLoader(
            self.test_dataset,
            batch_size=self.batch_size,
            shuffle=False,
            num_workers=self.num_workers,
            pin_memory=True if torch.cuda.is_available() else False
        )

        return train_loader, val_loader, test_loader

def get_aptos_dataloaders(data_dir='./data/APTOS', batch_size=32, num_workers=4):
    """便捷函数：直接获取APTOS数据加载器"""
    dataloader = APTOSDataLoader(data_dir, batch_size, num_workers)
    return dataloader.get_dataloaders()


if __name__ == "__main__":
    # 创建数据加载器
    aptos_loader = APTOSDataLoader(batch_size=32)

    # 获取DataLoader
    train_loader, val_loader, test_loader = aptos_loader.get_dataloaders()

    print(f"\nDataLoader创建成功!")
    print(f"训练批次数: {len(train_loader)}")
    print(f"验证批次数: {len(val_loader)}")
    print(f"测试批次数: {len(test_loader)}")