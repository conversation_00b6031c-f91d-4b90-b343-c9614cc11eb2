# 论文图表设计方案

## 图1 方法总览（One-page 总体架构示意）

### 布局与信息层级
- **画布走向**：自左向右贯穿整条推理路径，模块清晰分区，箭头表示数据流
- **分区建议**（从左到右）：

#### 1) 输入与骨干前端
- 输入彩色眼底图，标注分辨率（224×224）
- ResNet50 stem：Conv1 → BN1 → ReLU → MaxPool（合并为"Stem"方块）

#### 2) ResNet50 四个阶段（Layer1–Layer4）
- 四个并列方块，标注各自输出特征尺寸：
  - L1: 256×56×56
  - L2: 512×28×28
  - L3: 1024×14×14
  - L4: 2048×7×7
- 每个阶段方块上方加"EMA 残差注入 + DropPath"图标
- 强调"x + EMA(x) → DropPath"
- 用渐变色表现"渐进式增强"从L1到L4的累积

#### 3) 轻量化PPM（层级自适应）
- **L3**: 1×1降维到256，pool_scales=(1,3,6)
- **L4**: 1×1降维到512，pool_scales=(1,2,3,6)
- 内部简化插图：1×1降维 → 多分支自适应池化 → 上采样 → 1×1融合

#### 4) 交互式跨尺度融合（Cross-Scale Fusion, CSF）
步骤化展示：
- a) **通道对齐**：L3_ppm与L4_ppm各经1×1 Conv对齐到512通道；L3上采样到7×7
- b) **拼接**：Concat([L3', L4']) → 1024×7×7
- c) **二元通道注意**：输出α3、α4权重（[B,2,1,1]），分别加权L3'、L4'
- d) **空间注意**：生成单通道空间权重图S（[B,1,H,W]）
- e) **3×3融合卷积**：得到融合结果F（512×7×7）

#### 5) 分类头
- GAP（全局平均池化）→ FC(num_classes)，输出DR分级概率

### 形状与超参标注
- **关键节点形状**：
  - L3_ppm: 256×14×14
  - L4_ppm: 512×7×7
  - 融合对齐后：两路均为512×7×7
  - Concat后：1024×7×7
  - 3×3 Conv后：512×7×7
- **超参角落标注**：ema_factor=8, drop_prob, ppm3_dim=256, ppm4_dim=512, fusion_dim=512

### 图例与符号
- **图例包含**：EMA、DropPath、LPPM、Dual Channel α3/α4、Spatial S、3×3 Conv
- **统一符号**：α3、α4表示层级权重；S表示空间注意；F表示融合输出
- **配色方案**：L3=蓝色，L4=橙色，α3/α4用对应色深浅

### Caption要点
"Interactive Multi-Scale Feature Learning框架：从低到高的渐进式多尺度增强（EMA+DropPath）→ 层内轻量级多尺度上下文（L3/L4的LPPM）→ 层间交互式选择融合（Dual Channel + Spatial）→ 分类。强调层内多尺度+层间互补选择的一体化设计，针对DR病灶多尺度异质性。"

---

## 图2 解释性可视化（"关注模式 + 互补性"合并展示）

### 目标与信息密度
回答两个关键问题：
1) 方法是否更聚焦DR关键病灶（微小病灶、弥散病变）？
2) L3/L4的交互式融合是否体现"层级互补+选择性"？

### 推荐排版（3行×4列小面板）
#### 行设计（每行一个样本）：
- **正例（中/重度）×1**：展示对弥散病变的覆盖
- **轻度×1**：展示对微小病灶的敏感
- **失败/边界样本×1**：用于分析改进空间

#### 列设计（同一行从左到右）：
1) **原图**：原始眼底图，必要时用白框圈出病灶ROI
2) **Grad-CAM（Baseline ResNet50）**：叠加热力图
3) **Grad-CAM（Ours）**：叠加热力图（目标层：融合后特征F）
4) **交互式融合可视化**：
   - 上半：空间注意S的热力图叠加
   - 下半：α3/α4的柱状图（蓝=α3，橙=α4）

### 生成方式与实现提示
#### Grad-CAM设置：
- **Baseline目标层**：resnet50的layer4输出
- **Ours目标层**：Cross-Scale Fusion之后、GAP之前的特征F
- **归一化**：min-max到[0,1]，colormap叠加，透明度0.4

#### 空间注意S：
- 直接取spatial_attention输出（[B,1,H,W]），插值到输入大小后叠加

#### α3/α4权重：
- 取cross_attention输出（[B,2,1,1]），画成双柱条（蓝=α3，橙=α4）

### 样本选择策略
- 每个DR等级至少1个样本
- 优先选择预测正确但难度高的样本
- 保留1个失败样本用于分析
- 确保相同输入分辨率与归一化管线

### Caption要点
"解释性可视化对比：左列为不同DR等级原图；中间两列展示Baseline vs Ours的Grad-CAM，我们的模型对关键病灶区域响应更集中；右列显示空间注意S凸显判别区域，α3/α4体现L3/L4的互补与自适应选择（小病灶样本α3较高，语义强样本α4较高）。"

---

## 配色与版式统一要求

### 颜色方案
- **L3层级**：蓝色系（#1f77b4）
- **L4层级**：橙色系（#ff7f0e）
- **α3权重**：蓝色深浅变化
- **α4权重**：橙色深浅变化
- **空间注意S**：暖色伪彩（magma/turbo）
- **色盲友好**：确保蓝/橙对比度足够

### 符号统一
- α3、α4：层级权重
- S：空间注意
- F：融合输出
- EMA：高效多尺度注意
- LPPM：轻量化金字塔池化
- CSF：跨尺度融合

### 版式要求
- **分辨率**：DPI≥300
- **字体**：统一使用8-9pt
- **坐标轴**：相同范围与色条
- **箭头**：清晰数据流向
- **标注**：关键尺寸与超参

---

## 代码实现锚点

### Cross-Scale Fusion权重提取
```python
# 位置：models/Resnet50_EMA_PPM_D.py
channel_weights = self.cross_attention(concat_feat)  # [B, 2, 1, 1] → α3/α4
spatial_weight  = self.spatial_attention(concat_feat)  # [B, 1, H, W] → S
final_output    = self.final_fusion(spatial_enhanced)  # 3×3融合 → F
```

### 渐进式EMA注入
```python
# Layer1-4的EMA残差注入
x1_ema = x1 + self.ema_layer1(x1)  # 残差连接
x1_ema = self.drop_path(x1_ema)    # DropPath正则化
```

### LightweightPPM配置
```python
# L3与L4的差异化配置
self.ppm3 = LightweightPPM(1024, 256, pool_scales=(1, 3, 6))      # L3轻量级
self.ppm4 = LightweightPPM(2048, 512, pool_scales=(1, 2, 3, 6))   # L4完整版
```


---


