import torch
import torch.nn as nn
import torch.nn.functional as F

class PPM(nn.Module): # pspnet中的金字塔池化模块
    def __init__(self, down_dim):
        super(PPM, self).__init__()
        self.down_conv = nn.Sequential(
            nn.Conv2d(2048, down_dim, 3, padding=1),
            nn.BatchNorm2d(down_dim),
            nn.PReLU()
        )

        # 使用不同尺度的自适应平均池化，并通过1x1卷积来减少特征维度
        self.conv1 = nn.Sequential(
            nn.AdaptiveAvgPool2d(output_size=(1, 1)),
            nn.Conv2d(down_dim, down_dim, kernel_size=1),
            nn.BatchNorm2d(down_dim),
            nn.PReLU()
        )
        self.conv2 = nn.Sequential(
            nn.AdaptiveAvgPool2d(output_size=(2, 2)),
            nn.Conv2d(down_dim, down_dim, kernel_size=1),
            nn.BatchNorm2d(down_dim),
            nn.PReLU()
        )
        self.conv3 = nn.Sequential(
            nn.AdaptiveAvgPool2d(output_size=(3, 3)),
            nn.Conv2d(down_dim, down_dim, kernel_size=1),
            nn.BatchNorm2d(down_dim),
            nn.PReLU()
        )
        self.conv4 = nn.Sequential(
            nn.AdaptiveAvgPool2d(output_size=(6, 6)),
            nn.Conv2d(down_dim, down_dim, kernel_size=1),
            nn.BatchNorm2d(down_dim),
            nn.PReLU()
        )

        # 融合不同尺度的特征
        self.fuse = nn.Sequential(
            nn.Conv2d(4 * down_dim, down_dim, kernel_size=1),
            nn.BatchNorm2d(down_dim),
            nn.PReLU()
        )

    def forward(self, x):
        x = self.down_conv(x)  # 降维
        conv1 = self.conv1(x)  # 1x1尺度
        conv2 = self.conv2(x)  # 2x2尺度
        conv3 = self.conv3(x)  # 3x3尺度
        conv4 = self.conv4(x)  # 6x6尺度

        # 将池化后的特征上采样到输入特征相同的尺寸，并进行融合
        conv1_up = F.interpolate(conv1, size=x.size()[2:], mode='bilinear', align_corners=True)
        conv2_up = F.interpolate(conv2, size=x.size()[2:], mode='bilinear', align_corners=True)
        conv3_up = F.interpolate(conv3, size=x.size()[2:], mode='bilinear', align_corners=True)
        conv4_up = F.interpolate(conv4, size=x.size()[2:], mode='bilinear', align_corners=True)

        return self.fuse(torch.cat((conv1_up, conv2_up, conv3_up, conv4_up), 1))  # 在通道维度上进行拼接并通过1x1卷积融合

# 测试用例
if __name__ == "__main__":
    # 假设输入特征维度是2048，我们想要降维到512
    ppm = PPM(down_dim=512)
    input_tensor = torch.randn(64, 2048, 32, 32)  # 模拟输入
    output_tensor = ppm(input_tensor)
    print("Output shape:", output_tensor.shape)  # 打印输出维度