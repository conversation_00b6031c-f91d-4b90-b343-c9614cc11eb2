import torch
import torch.nn as nn
import torch.nn.functional as F
import torchvision.models as models
from torchvision.models import ResNet50_Weights
from .EMA import EMA


class DropPath(nn.Module):
    def __init__(self, drop_prob: float = 0.0):
        super(DropPath, self).__init__()
        self.drop_prob = drop_prob

    def forward(self, x: torch.Tensor) -> torch.Tensor:
        if self.drop_prob == 0.0 or not self.training:
            return x

        keep_prob = 1.0 - self.drop_prob
        mask = torch.ones(x.size(0), 1, 1, 1).to(x.device) * keep_prob
        mask = mask + torch.randn_like(mask) * 0.0
        mask = F.relu(mask)

        return x * mask / keep_prob


class PPM(nn.Module):
    """
    Pyramid Pooling Module for Sequential Integration
    
    优化版PPM模块，用于串行集成
    """
    def __init__(self, in_channels, down_dim, pool_scales=(1, 2, 3, 6)):
        super(PPM, self).__init__()
        
        self.pool_scales = pool_scales
        
        # 输入特征降维
        self.down_conv = nn.Sequential(
            nn.Conv2d(in_channels, down_dim, 1, bias=False),
            nn.BatchNorm2d(down_dim),
            nn.ReLU(inplace=True)
        )

        # 轻量级多尺度池化分支
        branch_dim = down_dim // len(pool_scales)
        self.pool_branches = nn.ModuleList()
        for scale in pool_scales:
            self.pool_branches.append(
                nn.Sequential(
                    nn.AdaptiveAvgPool2d(output_size=(scale, scale)),
                    nn.Conv2d(down_dim, branch_dim, kernel_size=1, bias=False),
                    nn.BatchNorm2d(branch_dim),
                    nn.ReLU(inplace=True)
                )
            )

        # 特征融合
        total_branch_dim = len(pool_scales) * branch_dim
        self.fuse = nn.Sequential(
            nn.Conv2d(total_branch_dim, down_dim, kernel_size=1, bias=False),
            nn.BatchNorm2d(down_dim),
            nn.ReLU(inplace=True)
        )

    def forward(self, x):
        # 输入特征降维
        x = self.down_conv(x)
        
        # 获取输入特征的空间尺寸
        input_size = x.size()[2:]
        
        # 多尺度池化
        pool_features = []
        for pool_branch in self.pool_branches:
            pool_feat = pool_branch(x)
            # 上采样到原始尺寸
            pool_feat = F.interpolate(pool_feat, size=input_size, 
                                    mode='bilinear', align_corners=False)
            pool_features.append(pool_feat)
        
        # 特征拼接和融合
        concat_features = torch.cat(pool_features, dim=1)
        fused_features = self.fuse(concat_features)
        
        return fused_features


class ResNet50_EMA_PPM_Sequential(nn.Module):
    """
    ResNet50 with EMA and PPM Sequential Integration (方案A)
    
    串行集成策略:
    1. EMA应用于所有stage (Layer1-4) - 局部多尺度注意力
    2. PPM应用于EMA增强后的Layer4 - 全局上下文聚合
    3. 特征融合层整合原始Layer4特征和PPM特征
    4. 串行处理，逻辑清晰，互补性强
    """
    
    def __init__(self, num_classes=1000, drop_prob=0.5, ema_factor=8,
                 ppm_dim=256, pool_scales=(1, 2, 3, 6)):
        super(ResNet50_EMA_PPM_Sequential, self).__init__()

        # 加载预训练的 ResNet50
        self.model = models.resnet50(weights=ResNet50_Weights.IMAGENET1K_V2)

        self.drop_path = DropPath(drop_prob)
        
        # EMA模块 - 应用于所有stage
        self.ema_layer1 = EMA(256, factor=ema_factor)
        self.ema_layer2 = EMA(512, factor=ema_factor)
        self.ema_layer3 = EMA(1024, factor=ema_factor)
        self.ema_layer4 = EMA(2048, factor=ema_factor)
        
        # PPM模块 - 应用于EMA增强后的Layer4
        self.ppm = PPM(2048, ppm_dim, pool_scales=pool_scales)
        
        # 特征融合层 - 融合原始Layer4特征和PPM特征
        self.feature_fusion = nn.Sequential(
            nn.Conv2d(2048 + ppm_dim, 2048, kernel_size=1, bias=False),
            nn.BatchNorm2d(2048),
            nn.ReLU(inplace=True),
            nn.Dropout2d(0.1)
        )
        
        # 修改最后的分类层
        self.model.fc = nn.Linear(self.model.fc.in_features, num_classes)

    def forward(self, x):
        # 通过ResNet50的前几层
        x = self.model.conv1(x)
        x = self.model.bn1(x)
        x = self.model.relu(x)
        x = self.model.maxpool(x)

        # Layer1 + EMA (残差连接)
        x1 = self.model.layer1(x)
        x1_ema = x1 + self.ema_layer1(x1)  # 残差连接
        x1_ema = self.drop_path(x1_ema)

        # Layer2 + EMA (残差连接)
        x2 = self.model.layer2(x1_ema)
        x2_ema = x2 + self.ema_layer2(x2)  # 残差连接
        x2_ema = self.drop_path(x2_ema)

        # Layer3 + EMA (残差连接)
        x3 = self.model.layer3(x2_ema)
        x3_ema = x3 + self.ema_layer3(x3)  # 残差连接
        x3_ema = self.drop_path(x3_ema)

        # Layer4 + EMA (残差连接)
        x4 = self.model.layer4(x3_ema)
        x4_ema = x4 + self.ema_layer4(x4)  # 残差连接
        x4_ema = self.drop_path(x4_ema)

        # PPM全局上下文聚合
        ppm_features = self.ppm(x4_ema)
        
        # 特征融合：原始Layer4特征 + PPM特征
        fused_features = torch.cat([x4_ema, ppm_features], dim=1)
        enhanced_features = self.feature_fusion(fused_features)

        # 全局平均池化和分类
        x = self.model.avgpool(enhanced_features)
        x = torch.flatten(x, 1)
        x = self.model.fc(x)

        return x
    
    def get_multi_scale_features(self, x):
        """
        获取多尺度特征，用于可视化和分析
        """
        # 前向传播到各层
        x = self.model.conv1(x)
        x = self.model.bn1(x)
        x = self.model.relu(x)
        x = self.model.maxpool(x)

        features = {}
        
        # Layer1 (残差连接)
        x1 = self.model.layer1(x)
        x1_ema = x1 + self.ema_layer1(x1)  # 残差连接
        features['layer1_original'] = x1
        features['layer1_ema'] = x1_ema

        # Layer2 (残差连接)
        x2 = self.model.layer2(x1_ema)
        x2_ema = x2 + self.ema_layer2(x2)  # 残差连接
        features['layer2_original'] = x2
        features['layer2_ema'] = x2_ema

        # Layer3 (残差连接)
        x3 = self.model.layer3(x2_ema)
        x3_ema = x3 + self.ema_layer3(x3)  # 残差连接
        features['layer3_original'] = x3
        features['layer3_ema'] = x3_ema

        # Layer4 (残差连接)
        x4 = self.model.layer4(x3_ema)
        x4_ema = x4 + self.ema_layer4(x4)  # 残差连接
        features['layer4_original'] = x4
        features['layer4_ema'] = x4_ema

        # PPM特征
        ppm_features = self.ppm(x4_ema)
        features['ppm_features'] = ppm_features
        
        # 融合特征
        fused_features = torch.cat([x4_ema, ppm_features], dim=1)
        enhanced_features = self.feature_fusion(fused_features)
        features['enhanced_features'] = enhanced_features
        
        return features


if __name__ == "__main__":
    # 创建串行集成模型
    model = ResNet50_EMA_PPM_Sequential(
        num_classes=5, 
        drop_prob=0.5, 
        ema_factor=8,
        ppm_dim=256,
        pool_scales=(1, 2, 3, 6)
    )
    
    # 打印模型参数数量
    total_params = sum(p.numel() for p in model.parameters())
    trainable_params = sum(p.numel() for p in model.parameters() if p.requires_grad)
    print(f"Total parameters: {total_params:,}")
    print(f"Trainable parameters: {trainable_params:,}")

    # 测试模型的输入输出
    x = torch.randn(2, 3, 224, 224)
    output = model(x)
    print(f"Input shape: {x.shape}")
    print(f"Output shape: {output.shape}")
    
    # 计算各模块的参数数量
    ema_params = (sum(p.numel() for p in model.ema_layer1.parameters()) +
                  sum(p.numel() for p in model.ema_layer2.parameters()) +
                  sum(p.numel() for p in model.ema_layer3.parameters()) +
                  sum(p.numel() for p in model.ema_layer4.parameters()))
    
    ppm_params = sum(p.numel() for p in model.ppm.parameters())
    fusion_params = sum(p.numel() for p in model.feature_fusion.parameters())
    
    print(f"\n模块参数分解:")
    print(f"  EMA模块总参数: {ema_params:,}")
    print(f"  PPM模块参数: {ppm_params:,}")
    print(f"  特征融合层参数: {fusion_params:,}")
    print(f"  新增模块总参数: {ema_params + ppm_params + fusion_params:,}")
    print(f"  新增模块占比: {((ema_params + ppm_params + fusion_params) / total_params * 100):.2f}%")
    
    print(f"\n方案A特点:")
    print(f"1. 串行集成 - EMA局部注意力 → PPM全局上下文")
    print(f"2. 互补性强 - 局部细节 + 全局信息")
    print(f"3. 参数合理 - 约1.5M新增参数")
    print(f"4. 实现简单 - 逻辑清晰，训练稳定")
