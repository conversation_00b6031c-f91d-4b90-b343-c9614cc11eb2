{"timestamp": "2025-08-04T05:04:46.361053", "test_results": {"accuracy": 0.8360655737704918, "qwk": 0.8782799561009678, "auc": 0.9211451075409487, "precision": 0.8372851336431627, "recall": 0.8360655737704918, "f1": 0.8252664576802509}, "training_history": {"train_loss": [0.7691878420503243, 0.605022598867831, 0.5486704217998878, 0.5075514015296231, 0.4923467205270477, 0.44887551568124606, 0.4166357044292533, 0.40668503826727037, 0.3880057521974263, 0.35900871261306433, 0.3332835750087448, 0.3169904160110847, 0.2999730452733195, 0.2976474652633719, 0.28152097267625126], "val_accuracy": [0.7431693989071039, 0.7486338797814208, 0.8114754098360656, 0.8087431693989071, 0.7978142076502732, 0.819672131147541, 0.8032786885245902, 0.8224043715846995, 0.8278688524590164, 0.819672131147541, 0.8278688524590164, 0.819672131147541, 0.8278688524590164, 0.8387978142076503, 0.8306010928961749], "val_qwk": [0.8019755278710358, 0.8067217381464298, 0.8659301371468761, 0.8612718427037453, 0.8611654168247206, 0.863260144844235, 0.8598554583491822, 0.8670102943936703, 0.8605429302487547, 0.8433724021266312, 0.8709829034721814, 0.8697763328635812, 0.8712136762161686, 0.8839678619084121, 0.8710396756443672], "val_auc": [0.8839360862105192, 0.8996594929385789, 0.93013227423615, 0.9189872044357379, 0.9237381628117032, 0.9333509989064848, 0.9264387213919802, 0.9345566756467907, 0.9422160773705202, 0.9381671909675354, 0.935542333430298, 0.9351555112698471, 0.9379753148966117, 0.9362574801915986, 0.9358242524585952], "val_precision": [0.7498742150381494, 0.7309010394767547, 0.8055903134653081, 0.7792913644570683, 0.8045857674849726, 0.8058749083339248, 0.7819996650126667, 0.8128110477239244, 0.8170051715133683, 0.8107811903376032, 0.8189946432298041, 0.81291392845093, 0.8159379630691106, 0.8292917247478901, 0.8223656571014392], "val_recall": [0.7431693989071039, 0.7486338797814208, 0.8114754098360656, 0.8087431693989071, 0.7978142076502732, 0.819672131147541, 0.8032786885245902, 0.8224043715846995, 0.8278688524590164, 0.819672131147541, 0.8278688524590164, 0.819672131147541, 0.8278688524590164, 0.8387978142076503, 0.8306010928961749], "val_f1": [0.7201850181590878, 0.7045437563647147, 0.8035814124878418, 0.7823749646178348, 0.7904280929641629, 0.8061410195684281, 0.7776581832691939, 0.815017675972054, 0.8121097769000872, 0.8092145908092407, 0.8180895710912698, 0.8141468723602531, 0.8173457389824047, 0.8312964089689266, 0.8217994163241812], "learning_rate": [0.0001, 9.890738003669029e-05, 9.567727288213005e-05, 9.045084971874738e-05, 8.345653031794292e-05, 7.500000000000001e-05, 6.545084971874738e-05, 5.522642316338269e-05, 4.4773576836617344e-05, 3.454915028125264e-05, 2.5000000000000018e-05, 1.654346968205711e-05, 9.549150281252635e-06, 4.322727117869952e-06, 1.092619963309716e-06], "epochs": [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15], "best_epoch": 14, "best_qwk": 0.8839678619084121}, "training_config": {"model": "ResNet50_EMA_PPM_Interactive", "num_classes": 5, "epochs": 15, "batch_size": 32, "device": "cuda", "optimizer": "<PERSON>", "scheduler": "CosineAnnealingLR", "criterion": "CrossEntropyLoss", "ema_factor": 8, "ppm3_dim": 256, "ppm4_dim": 512, "fusion_dim": 512, "integration_scheme": "Interactive Integration (方案D)", "innovation": "EMA + PPM with cross-scale feature interaction", "features": ["EMA applied to all layers", "PPM applied to Layer3 and Layer4", "Cross-scale feature fusion", "Maximum feature utilization"]}}