{"timestamp": "2025-08-04T03:29:04.407843", "test_results": {"accuracy": 0.7595628415300546, "qwk": 0.7447904095261083, "auc": 0.8843390348894044, "precision": 0.6651764711964473, "recall": 0.7595628415300546, "f1": 0.6896244810000636}, "training_history": {"train_loss": [1.282031396160955, 1.0616662839184636, 0.9579256215821141, 0.8841873031595479, 0.8378564281308133, 0.8175916347814642, 0.8039814036177553, 0.7782202223720758, 0.7753539811009946, 0.7569022253155708, 0.7516828765687735, 0.7499305094065873, 0.743044288262077, 0.7406578731277714, 0.7368649684864542], "val_accuracy": [0.6311475409836066, 0.6857923497267759, 0.6994535519125683, 0.7049180327868853, 0.7131147540983607, 0.7240437158469946, 0.7377049180327869, 0.726775956284153, 0.726775956284153, 0.7431693989071039, 0.7404371584699454, 0.7404371584699454, 0.7486338797814208, 0.73224043715847, 0.7349726775956285], "val_qwk": [0.48438646295174004, 0.6428314415622649, 0.6575459738891358, 0.6725251490774542, 0.7031999867151564, 0.710378024938803, 0.7316508451030331, 0.7328028873759331, 0.7121931973236449, 0.7372208559591992, 0.7363924259418091, 0.7333366078884032, 0.7326186528327286, 0.7079336547103521, 0.7257059698807986], "val_auc": [0.7128922560692239, 0.78796042721962, 0.8284360756268084, 0.8462431135377122, 0.8544340760921102, 0.8594337208132364, 0.8618810732775026, 0.8638852431227667, 0.8690382601268244, 0.8705125805006556, 0.8702911362854137, 0.8734587940083655, 0.8738785029691624, 0.8681010461493249, 0.8730114950527268], "val_precision": [0.46850875236319833, 0.5250712962888299, 0.650473144075703, 0.5860384938809531, 0.6350512574950635, 0.6788898138068913, 0.7024341778440139, 0.7217077301632955, 0.6922315446622477, 0.7105290639876807, 0.7167932249899462, 0.705868157356006, 0.7050438491162437, 0.6867760896759133, 0.6998030417462644], "val_recall": [0.6311475409836066, 0.6857923497267759, 0.6994535519125683, 0.7049180327868853, 0.7131147540983607, 0.7240437158469946, 0.7377049180327869, 0.726775956284153, 0.726775956284153, 0.7431693989071039, 0.7404371584699454, 0.7404371584699454, 0.7486338797814208, 0.73224043715847, 0.7349726775956285], "val_f1": [0.5353030942678499, 0.593972083014808, 0.6124723167561205, 0.6192507496410518, 0.6324859583485984, 0.657570188421458, 0.6781443565925755, 0.6523890450646327, 0.6625326697235642, 0.6818539557873897, 0.6797953911328793, 0.6786103273398985, 0.6911697265974072, 0.6718355905943534, 0.6733320779711246], "learning_rate": [0.0001, 9.890738003669029e-05, 9.567727288213005e-05, 9.045084971874738e-05, 8.345653031794292e-05, 7.500000000000001e-05, 6.545084971874738e-05, 5.522642316338269e-05, 4.4773576836617344e-05, 3.454915028125264e-05, 2.5000000000000018e-05, 1.654346968205711e-05, 9.549150281252635e-06, 4.322727117869952e-06, 1.092619963309716e-06], "epochs": [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15], "best_epoch": 10, "best_qwk": 0.7372208559591992}, "training_config": {"model": "ResNet50_EMA_AllStages", "num_classes": 5, "epochs": 15, "batch_size": 32, "device": "cuda", "optimizer": "<PERSON>", "scheduler": "CosineAnnealingLR", "criterion": "CrossEntropyLoss", "ema_factor": 8, "use_residual": true, "ema_stages": "all_stages (layer1, layer2, layer3, layer4)"}}