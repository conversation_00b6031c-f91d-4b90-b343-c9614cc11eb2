{"timestamp": "2025-08-05T03:03:13.299695", "test_results": {"accuracy": 0.7106227106227107, "auc": 0.7966429175220384, "precision": 0.7350452946020813, "recall": 0.7106227106227107, "f1": 0.7029052387971291}, "training_history": {"train_loss": [0.6259158834626403, 0.5393341059171701, 0.4928985415380212, 0.4576638110830814, 0.4116823737379871, 0.37837976481341107, 0.33264308960377414, 0.31392928184587743, 0.2691475270858294, 0.23749470107163054, 0.19806623440000076, 0.1979388930375063, 0.16256847996500473, 0.1555880987191502, 0.15823414553947088], "val_accuracy": [0.6776556776556777, 0.7161172161172161, 0.7380952380952381, 0.7380952380952381, 0.7509157509157509, 0.73992673992674, 0.7564102564102564, 0.76007326007326, 0.7527472527472527, 0.7509157509157509, 0.7380952380952381, 0.7344322344322345, 0.7490842490842491, 0.73992673992674, 0.73992673992674], "val_auc": [0.7574769552791532, 0.8032980450562868, 0.8333266245354158, 0.8291000818473346, 0.8230755813173396, 0.8328570086811845, 0.8318372713977109, 0.8240684834091428, 0.8246722752217257, 0.8146359135370125, 0.8138040225952314, 0.8176817077915979, 0.8233841860215487, 0.8216667337546459, 0.822136349608877], "val_precision": [0.6786143074909954, 0.7807662808534375, 0.7664094403074706, 0.7873218911917099, 0.7696733722690894, 0.7825193939298185, 0.7900728597449909, 0.7757026627218935, 0.7875702247191011, 0.768573439227908, 0.7416258169934641, 0.7367479674796747, 0.7566924778761063, 0.7428594711322984, 0.74097432787548], "val_recall": [0.6776556776556777, 0.7161172161172161, 0.7380952380952381, 0.7380952380952381, 0.7509157509157509, 0.73992673992674, 0.7564102564102564, 0.76007326007326, 0.7527472527472527, 0.7509157509157509, 0.7380952380952381, 0.7344322344322345, 0.7490842490842491, 0.73992673992674, 0.73992673992674], "val_f1": [0.677222587969743, 0.6987773842786211, 0.7309464325712021, 0.7263752553908049, 0.7465077219286387, 0.7297406581148912, 0.7491320254122231, 0.7566240544963949, 0.7450283820665464, 0.7467532467532468, 0.7371350079285722, 0.7337812345529563, 0.7472111170063567, 0.7391392118863049, 0.7396437782076081], "learning_rate": [0.0001, 9.890738003669029e-05, 9.567727288213005e-05, 9.045084971874738e-05, 8.345653031794292e-05, 7.500000000000001e-05, 6.545084971874738e-05, 5.522642316338269e-05, 4.4773576836617344e-05, 3.454915028125264e-05, 2.5000000000000018e-05, 1.654346968205711e-05, 9.549150281252635e-06, 4.322727117869952e-06, 1.092619963309716e-06], "epochs": [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15], "best_epoch": 3, "best_auc": 0.8333266245354158}, "training_config": {"model": "ResNet50_EMA_AllStages", "num_classes": 2, "epochs": 15, "batch_size": 32, "device": "mps", "optimizer": "<PERSON>", "scheduler": "CosineAnnealingLR", "criterion": "CrossEntropyLoss", "ema_factor": 8}}