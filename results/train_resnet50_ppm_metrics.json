{"timestamp": "2025-08-04T04:24:38.484005", "test_results": {"accuracy": 0.8060109289617486, "qwk": 0.836601767915298, "auc": 0.9247318056054568, "precision": 0.800326797385621, "recall": 0.8060109289617486, "f1": 0.7869199088693795}, "training_history": {"train_loss": [0.8362423498993334, 0.6518076217692831, 0.6173799229056939, 0.5849152547509774, 0.550344271828299, 0.5310661212905593, 0.5144568730307661, 0.5050633045966211, 0.4982689510545005, 0.47190886314796365, 0.45652579872504523, 0.4538567077530467, 0.43521629743601964, 0.45135327104641043, 0.42544571504644724], "val_accuracy": [0.7513661202185792, 0.7540983606557377, 0.7595628415300546, 0.7622950819672131, 0.7978142076502732, 0.7896174863387978, 0.7841530054644809, 0.7814207650273224, 0.8032786885245902, 0.8278688524590164, 0.8169398907103825, 0.8087431693989071, 0.8087431693989071, 0.819672131147541, 0.8278688524590164], "val_qwk": [0.7933501892155405, 0.7933919973435165, 0.8256945901877533, 0.8135696821515892, 0.8473069398089539, 0.8549082158047907, 0.8109774805178868, 0.8350020534462492, 0.856693950231187, 0.8650114929400511, 0.845797345692016, 0.8395076662533644, 0.8423724971666038, 0.8465408805031447, 0.8517398778825064], "val_auc": [0.8730491859625451, 0.888912104133156, 0.8981198862340634, 0.9047162367993252, 0.9115700576098312, 0.9109374595663802, 0.9225480186121467, 0.9267540255731108, 0.9231276740320759, 0.9247611680796222, 0.9239838452525809, 0.9283962236026667, 0.9274112313369969, 0.9291442707125876, 0.9273330033598348], "val_precision": [0.7479662965446802, 0.7287357737312024, 0.725649801283159, 0.7551589476780797, 0.7829341655155286, 0.7835108516526641, 0.7528706814135321, 0.7823549149669432, 0.802803906910796, 0.8189883141601679, 0.7983507855752061, 0.7948933736949565, 0.7930566397779513, 0.8023093937028363, 0.8139174705604995], "val_recall": [0.7513661202185792, 0.7540983606557377, 0.7595628415300546, 0.7622950819672131, 0.7978142076502732, 0.7896174863387978, 0.7841530054644809, 0.7814207650273224, 0.8032786885245902, 0.8278688524590164, 0.8169398907103825, 0.8087431693989071, 0.8087431693989071, 0.819672131147541, 0.8278688524590164], "val_f1": [0.6821843446112381, 0.6890557034538224, 0.7083595176098987, 0.7491744929446297, 0.7888807193219144, 0.7690756465791003, 0.7486040087857859, 0.7607689024903681, 0.7805353809477086, 0.8121599058287128, 0.794797691223694, 0.787364069903033, 0.7869203817098325, 0.8020191877761746, 0.809514338484309], "learning_rate": [0.0001, 9.890738003669029e-05, 9.567727288213005e-05, 9.045084971874738e-05, 8.345653031794292e-05, 7.500000000000001e-05, 6.545084971874738e-05, 5.522642316338269e-05, 4.4773576836617344e-05, 3.454915028125264e-05, 2.5000000000000018e-05, 1.654346968205711e-05, 9.549150281252635e-06, 4.322727117869952e-06, 1.092619963309716e-06], "epochs": [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15], "best_epoch": 10, "best_qwk": 0.8650114929400511}, "training_config": {"model": "ResNet50_PPM", "num_classes": 5, "epochs": 15, "batch_size": 32, "device": "cuda", "optimizer": "<PERSON>", "scheduler": "CosineAnnealingLR", "criterion": "CrossEntropyLoss", "ppm_dim": 512, "pool_scales": [1, 2, 3, 6], "module": "Pyramid Pooling Module (PPM)", "innovation": "Multi-scale context aggregation for enhanced feature representation"}}