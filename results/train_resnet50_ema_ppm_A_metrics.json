{"timestamp": "2025-08-04T04:09:13.800059", "test_results": {"accuracy": 0.7841530054644809, "qwk": 0.8119889841754542, "auc": 0.9230575401504624, "precision": 0.7656438332657252, "recall": 0.7841530054644809, "f1": 0.7628103948021384}, "training_history": {"train_loss": [0.874464594475601, 0.6625046250612839, 0.6128101938444636, 0.5631506857664689, 0.554295574841292, 0.5315810234650321, 0.5221285925287268, 0.5039875873405001, 0.49939365328654, 0.4807994425620722, 0.48062458342832065, 0.46259751851144043, 0.46181139673875726, 0.47074040300820186, 0.46443868298893387], "val_accuracy": [0.7513661202185792, 0.7650273224043715, 0.76775956284153, 0.7841530054644809, 0.7786885245901639, 0.7814207650273224, 0.7950819672131147, 0.7759562841530054, 0.8087431693989071, 0.819672131147541, 0.8306010928961749, 0.825136612021858, 0.8142076502732241, 0.8278688524590164, 0.8169398907103825], "val_qwk": [0.7823680437464573, 0.8280108176534898, 0.8081022085259374, 0.8494653139566767, 0.825975254157144, 0.8466066336063386, 0.8453986657732979, 0.8386465452655077, 0.8556170300896476, 0.8502046384720328, 0.8706063237617984, 0.8626638238232268, 0.85970071037615, 0.8663564115414907, 0.8557024158017683], "val_auc": [0.8925300622045054, 0.9011756878418407, 0.9182736781275406, 0.924890034162614, 0.9266258427010164, 0.9232587355834628, 0.9258977343448059, 0.9260449029316972, 0.9291948142471789, 0.928876729291682, 0.9301832616350703, 0.9319036972319541, 0.9286120209622787, 0.9323824655391897, 0.9325208145109591], "val_precision": [0.7454764907223923, 0.7536695999810753, 0.7197396864419199, 0.7484701860255664, 0.7386519521244673, 0.7651833960092663, 0.7757998550031915, 0.7607528476447546, 0.7883677838805158, 0.8157370668829431, 0.8124302004292361, 0.8093363374168543, 0.8002629862992503, 0.8110950512542684, 0.797685424855399], "val_recall": [0.7513661202185792, 0.7650273224043715, 0.76775956284153, 0.7841530054644809, 0.7786885245901639, 0.7814207650273224, 0.7950819672131147, 0.7759562841530054, 0.8087431693989071, 0.819672131147541, 0.8306010928961749, 0.825136612021858, 0.8142076502732241, 0.8278688524590164, 0.8169398907103825], "val_f1": [0.6816797574895008, 0.7279323362247471, 0.7137141344791619, 0.7474557690222126, 0.7333512982983551, 0.7584324649551981, 0.7696526590963378, 0.7422886256465202, 0.7912254385580672, 0.8010349590247489, 0.812880962944224, 0.8042925929489353, 0.800110109169907, 0.8140562100461333, 0.798309120434887], "learning_rate": [0.0001, 9.890738003669029e-05, 9.567727288213005e-05, 9.045084971874738e-05, 8.345653031794292e-05, 7.500000000000001e-05, 6.545084971874738e-05, 5.522642316338269e-05, 4.4773576836617344e-05, 3.454915028125264e-05, 2.5000000000000018e-05, 1.654346968205711e-05, 9.549150281252635e-06, 4.322727117869952e-06, 1.092619963309716e-06], "epochs": [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15], "best_epoch": 11, "best_qwk": 0.8706063237617984}, "training_config": {"model": "ResNet50_EMA_PPM_Sequential", "num_classes": 5, "epochs": 15, "batch_size": 32, "device": "cuda", "optimizer": "<PERSON>", "scheduler": "CosineAnnealingLR", "criterion": "CrossEntropyLoss", "ema_factor": 8, "ppm_dim": 256, "pool_scales": [1, 2, 3, 6], "use_residual": true, "integration_scheme": "Sequential Integration (方案A)", "innovation": "EMA (local multi-scale attention) → PPM (global context aggregation)", "advantages": ["Complementary features: local details + global context", "Reasonable parameter increase (~1.5M)", "Simple implementation and stable training", "Best balance of performance and efficiency"]}}