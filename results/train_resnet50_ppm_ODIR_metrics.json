{"timestamp": "2025-08-05T04:48:32.169949", "test_results": {"accuracy": 0.7307692307692307, "auc": 0.8037810785063533, "precision": 0.7330487804878049, "recall": 0.7307692307692307, "f1": 0.7301092515812728}, "training_history": {"train_loss": [0.6309466848645029, 0.5656224142147016, 0.5240826957587954, 0.47719168964820574, 0.4430065358741374, 0.40184593200683594, 0.35264275986937027, 0.3128248411643354, 0.29042428147189225, 0.27077304354951354], "val_accuracy": [0.6410256410256411, 0.6904761904761905, 0.7289377289377289, 0.6996336996336996, 0.7161172161172161, 0.7435897435897436, 0.7435897435897436, 0.7454212454212454, 0.7472527472527473, 0.7472527472527473], "val_auc": [0.7245501750996257, 0.7715520133102551, 0.8026674180520335, 0.7990312495807002, 0.8016208455768895, 0.8035127265896497, 0.823867219471615, 0.8172925975123778, 0.818446510754203, 0.819573588804358], "val_precision": [0.6975175239133294, 0.693659281894576, 0.747092812767005, 0.7065626344944398, 0.7162593984962407, 0.7513603322949117, 0.748933894609826, 0.7500136686714052, 0.7551401869158878, 0.7506972314808515], "val_recall": [0.6410256410256411, 0.6904761904761905, 0.7289377289377289, 0.6996336996336996, 0.7161172161172161, 0.7435897435897436, 0.7435897435897436, 0.7454212454212454, 0.7472527472527473, 0.7472527472527473], "val_f1": [0.6133815028901735, 0.6891990663240044, 0.7238655002733734, 0.697093526737389, 0.7160705478969493, 0.7415926331588981, 0.7422061541367075, 0.7442467826127981, 0.7452841669709139, 0.7463815922340554], "learning_rate": [0.0001, 9.755282581475769e-05, 9.045084971874737e-05, 7.938926261462366e-05, 6.545084971874737e-05, 4.9999999999999996e-05, 3.454915028125263e-05, 2.0610737385376345e-05, 9.549150281252631e-06, 2.447174185242323e-06], "epochs": [1, 2, 3, 4, 5, 6, 7, 8, 9, 10], "best_epoch": 7, "best_auc": 0.823867219471615}, "training_config": {"model": "ResNet50_PPM", "num_classes": 2, "epochs": 10, "batch_size": 32, "device": "cuda", "optimizer": "<PERSON>", "scheduler": "CosineAnnealingLR", "criterion": "CrossEntropyLoss", "ppm_dim": 512, "pool_scales": [1, 2, 3, 6]}}